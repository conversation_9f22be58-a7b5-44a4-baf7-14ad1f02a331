const Point = require('../models/Point');
const Community = require('../models/Community');
const User = require('../models/User');
const { generateUniqueId } = require('../../utils/idGenerator');

// Create a new point
const createPoint = async (pointData) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid: pointData.creatorId });
    if (!user) throw new Error('User not found');
    
    // Generate a unique point ID
    const pointId = generateUniqueId('PNT');
    
    // Create the point
    const point = await Point.create({
      pointId,
      communityId: pointData.communityId,
      creatorId: user.pointId,
      position: pointData.position,
      metadata: pointData.metadata || {}
    });
    
    return point;
  } catch (error) {
    throw error;
  }
};

// Get all points for a community
const getPointsByCommunityId = async (communityId, firebaseUid) => {
  try {
    // Check if user has permission to view this community
    const community = await Community.findOne({ communityId });
    
    if (!community) throw new Error('Community not found');
    
    // If community is private, check if user is a member
    if (community.settings.visibility === 'private') {
      const isMember = community.members.some(
        member => member.firebaseUid === firebaseUid
      );
      
      if (!isMember) {
        throw new Error('You do not have permission to view points in this community');
      }
    }
    
    // Get all points for this community
    const points = await Point.find({ communityId });
    return points;
  } catch (error) {
    throw error;
  }
};

// Get public points for a community (no auth required)
const getPublicPointsByCommunityId = async (communityId) => {
  try {
    // Check if community is public
    const community = await Community.findOne({
      communityId,
      'settings.visibility': 'public'
    });
    
    if (!community) {
      throw new Error('Community not found or is not public');
    }
    
    // Get all points for this community
    const points = await Point.find({ communityId });
    return points;
  } catch (error) {
    throw error;
  }
};

// Check if user has permission to add points to a community
const checkUserPermission = async (firebaseUid, communityId) => {
  try {
    const community = await Community.findOne({ communityId });
    if (!community) return false;
    
    // Check if user is a member of this community
    const isMember = community.members.some(
      member => member.firebaseUid === firebaseUid
    );
    
    return isMember;
  } catch (error) {
    return false;
  }
};

// Check if user has permission to edit a specific point
const checkPointEditPermission = async (firebaseUid, pointId) => {
  try {
    // Get the point
    const point = await Point.findOne({ pointId });
    if (!point) return false;
    
    // Get the user
    const user = await User.findOne({ firebaseUid });
    if (!user) return false;
    
    // Check if user created this point
    if (point.creatorId === user.pointId) return true;
    
    // Check if user is founder/admin of the community
    const community = await Community.findOne({ communityId: point.communityId });
    if (!community) return false;
    
    const member = community.members.find(m => m.firebaseUid === firebaseUid);
    if (!member) return false;
    
    return ['founder', 'admin'].includes(member.role);
  } catch (error) {
    return false;
  }
};

// Update a point
const updatePoint = async (pointId, updateData) => {
  try {
    const updatedPoint = await Point.findOneAndUpdate(
      { pointId },
      {
        $set: {
          position: updateData.position,
          metadata: updateData.metadata,
          lastModified: new Date()
        }
      },
      { new: true }
    );
    
    if (!updatedPoint) {
      throw new Error('Point not found');
    }
    
    return updatedPoint;
  } catch (error) {
    throw error;
  }
};

// Delete a point
const deletePoint = async (pointId) => {
  try {
    const result = await Point.deleteOne({ pointId });
    
    if (result.deletedCount === 0) {
      throw new Error('Point not found');
    }
    
    return true;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createPoint,
  getPointsByCommunityId,
  getPublicPointsByCommunityId,
  checkUserPermission,
  checkPointEditPermission,
  updatePoint,
  deletePoint
};