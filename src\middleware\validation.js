const { body, param, validationResult } = require('express-validator');

// Middleware to check for validation errors
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// User registration validation rules
const userValidationRules = [
  body('email').isEmail().withMessage('Must provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('Username can only contain letters, numbers and underscores')
];

// Community validation rules
const communityValidationRules = [
  body('name').isLength({ min: 2, max: 50 }).withMessage('Community name must be between 2 and 50 characters'),
  body('settings.visibility').isIn(['public', 'private']).withMessage('Visibility must be either public or private')
];

// Point validation rules
const pointValidationRules = [
  body('position.x').isNumeric().withMessage('X position must be a number'),
  body('position.y').isNumeric().withMessage('Y position must be a number'),
  body('position.z').isNumeric().withMessage('Z position must be a number'),
  body('metadata.title').optional().isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters'),
  param('communityId').isString().withMessage('Community ID must be provided')
];

module.exports = {
  validate,
  userValidationRules,
  communityValidationRules,
  pointValidationRules
};