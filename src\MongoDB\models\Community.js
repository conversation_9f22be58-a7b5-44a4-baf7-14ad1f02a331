const mongoose = require('mongoose');

const memberSchema = new mongoose.Schema({
  pointId: { 
    type: String, 
    required: true 
  },
  firebaseUid: { 
    type: String, 
    required: true 
  },
  role: { 
    type: String, 
    required: true,
    enum: ['founder', 'admin', 'member'],
    default: 'member'
  },
  joinedAt: { 
    type: Date, 
    default: Date.now 
  }
});

const communitySchema = new mongoose.Schema({
  communityId: { 
    type: String, 
    required: true, 
    unique: true 
  },
  name: { 
    type: String, 
    required: true 
  },
  ownerId: { 
    type: String, 
    required: true 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  settings: {
    visibility: { 
      type: String, 
      enum: ['public', 'private'], 
      default: 'private' 
    },
    maxMembers: { 
      type: Number, 
      default: 100 
    },
    // Add other settings as needed
  },
  members: [memberSchema]
});

// Index on common queries
communitySchema.index({ communityId: 1 });
communitySchema.index({ ownerId: 1 });
communitySchema.index({ 'members.firebaseUid': 1 });

module.exports = mongoose.model('Community', communitySchema);