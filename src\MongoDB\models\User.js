/**
 * User Model
 *
 * This model represents a user in the system. It stores user information
 * that is linked to Firebase Authentication via the firebaseUid field.
 */

const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // Firebase Authentication UID
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  // User's chosen username
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },

  // Unique identifier for the user's point in the system
  pointId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  // Community the user belongs to (optional)
  communityId: {
    type: String,
    index: true
  },

  // User's email from Firebase (for easy reference)
  email: {
    type: String,
    sparse: true,
    index: true
  },

  // User's display name (may differ from username)
  displayName: {
    type: String,
    trim: true
  },

  // User's profile information
  profile: {
    bio: { type: String, maxlength: 500 },
    avatarUrl: { type: String },
    preferences: {
      theme: { type: String, enum: ['light', 'dark', 'system'], default: 'system' },
      notifications: { type: <PERSON>olean, default: true }
    }
  },

  // User's point position in 3D space (if applicable)
  pointPosition: {
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 }
  },

  // Timestamps
  createdAt: { type: Date, default: Date.now },
  lastLogin: { type: Date, default: Date.now },

  // Account status
  isActive: { type: Boolean, default: true },
  isVerified: { type: Boolean, default: false }
});

// Create indexes for common queries
userSchema.index({ username: 1 });
userSchema.index({ pointId: 1 });
userSchema.index({ communityId: 1 });
userSchema.index({ createdAt: -1 });

module.exports = mongoose.model('User', userSchema);