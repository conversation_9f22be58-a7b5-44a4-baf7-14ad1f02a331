const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./routes/auth');
const communityRoutes = require('./routes/community');
const pointRoutes = require('./routes/point');
const publicRoutes = require('./routes/public');
const userRoutes = require('./routes/user');

// Map routes
router.use('/auth', authRoutes);
router.use('/communities', communityRoutes);
router.use('/points', pointRoutes);
router.use('/public', publicRoutes);
router.use('/user', userRoutes);

module.exports = router;