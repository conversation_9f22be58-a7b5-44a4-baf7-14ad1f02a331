# Homara Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
NODE_ENV=development
PORT=3000

# Frontend URLs (for CORS)
FRONTEND_URL=http://localhost:3002

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/homara
# For MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/homara?retryWrites=true&w=majority

# Firebase Configuration
# Get these from Firebase Console > Project Settings > Service Accounts
FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com

# Firebase Service Account (for production)
# Base64 encoded service account JSON
# FIREBASE_SERVICE_ACCOUNT_BASE64=your_base64_encoded_service_account_json

# Telnyx Configuration
# Get API key from Telnyx Console
TELNYX_API_KEY=your_telnyx_api_key
TELNYX_BUCKET_NAME=homara-media

# Security Configuration
# Generate a strong secret for JWT tokens (if using custom JWT)
JWT_SECRET=your_super_secret_jwt_key_here

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (if using custom email service)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Development/Testing Configuration
MOCK_FIREBASE=false
MOCK_TELNYX=false

# Admin Configuration
# Default admin user (will be created on first run)
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=change_this_password

# Performance Configuration
CACHE_TTL=300000
MAX_FILE_SIZE=********
MAX_FILES_PER_REQUEST=5

# Database Connection Pool
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=2
DB_CONNECTION_TIMEOUT=30000

# WebSocket Configuration
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000


# Development
MONGODB_URI_DEV=mongodb://localhost:27017

# Production
MONGODB_URI_PROD=mongodb+srv://username:<EMAIL>/homara

# Node Environment
NODE_ENV=development

# Server Configuration
PORT=3002
FRONTEND_URL=https://localhost:3001


# Firebase Configuration
# For development, create a firebase-service-account.json file in the root directory
# For production, encode the service account JSON as base64 and set it here
# FIREBASE_SERVICE_ACCOUNT_BASE64=

# Firebase Configuration
const firebaseConfig = {
    apiKey: "AIzaSyB-WtgN67M4vQN3BZXuJzkgAewPN_ZUoLw",
    authDomain: "homara-77657.firebaseapp.com",
    projectId: "homara-77657",
    storageBucket: "homara-77657.appspot.com",
    messagingSenderId: "************",
    appId: "1:************:web:6d237b055d2af050319ef4",
    measurementId: "G-PW4TDPPCFS"
  };

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes in milliseconds
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=debug

# Security
JWT_SECRET=your-jwt-secret-for-additional-security
CORS_ORIGIN=https://localhost:3001,http://localhost:3002

# Telnyx Configuration
TELNYX_API_KEY=your-telnyx-api-key
TELNYX_PUBLIC_KEY=your-telnyx-public-key