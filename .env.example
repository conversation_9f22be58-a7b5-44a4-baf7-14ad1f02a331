# Homara Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3002

# =============================================================================
# FRONTEND CONFIGURATION (CORS)
# =============================================================================
# Multiple frontend URLs separated by commas
FRONTEND_URL=https://localhost:3001
CORS_ORIGIN=https://localhost:3001,http://localhost:3002

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Primary MongoDB URI (switches based on NODE_ENV)
MONGODB_URI=mongodb://localhost:27017/homara

# Environment-specific URIs
MONGODB_URI_DEV=mongodb://localhost:27017/homara
MONGODB_URI_PROD=mongodb+srv://username:<EMAIL>/homara?retryWrites=true&w=majority

# Database Connection Pool Settings
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=2
DB_CONNECTION_TIMEOUT=30000

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
# Your actual Firebase project configuration
FIREBASE_API_KEY=AIzaSyB-WtgN67M4vQN3BZXuJzkgAewPN_ZUoLw
FIREBASE_AUTH_DOMAIN=homara-77657.firebaseapp.com
FIREBASE_PROJECT_ID=homara-77657
FIREBASE_STORAGE_BUCKET=homara-77657.appspot.com
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:6d237b055d2af050319ef4
FIREBASE_MEASUREMENT_ID=G-PW4TDPPCFS

# Firebase URLs for backend
FIREBASE_DATABASE_URL=https://homara-77657-default-rtdb.firebaseio.com
FIREBASE_STORAGE_BUCKET=homara-77657.appspot.com

# Firebase Service Account (for production)
# For development: create firebase-service-account.json in root directory
# For production: encode the service account JSON as base64 and set below
# FIREBASE_SERVICE_ACCOUNT_BASE64=your_base64_encoded_service_account_json

# =============================================================================
# TELNYX CONFIGURATION
# =============================================================================
TELNYX_API_KEY=your_telnyx_api_key
TELNYX_PUBLIC_KEY=your_telnyx_public_key
TELNYX_BUCKET_NAME=homara-media

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate a strong secret for JWT tokens
JWT_SECRET=your_super_secret_jwt_key_here

# Rate Limiting (15 minutes window, 100 requests max)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================
# Default admin user (created on first run if doesn't exist)
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=change_this_password

# =============================================================================
# PERFORMANCE & CACHING
# =============================================================================
# Cache TTL in milliseconds (5 minutes)
CACHE_TTL=300000

# File upload limits
MAX_FILE_SIZE=52428800
MAX_FILES_PER_REQUEST=5

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# =============================================================================
# DEVELOPMENT/TESTING FLAGS
# =============================================================================
# Set to true to use mock services during development
MOCK_FIREBASE=false
MOCK_TELNYX=false