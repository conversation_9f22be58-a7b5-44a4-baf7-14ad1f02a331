/**
 * Telnyx Storage Service
 * 
 * This module handles file storage and media management using Telnyx.
 * It provides functionality for uploading, downloading, and managing media files.
 */

const telnyx = require('telnyx');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class TelnyxService {
  constructor() {
    this.client = null;
    this.bucketName = process.env.TELNYX_BUCKET_NAME || 'homara-media';
    this.initialize();
  }

  /**
   * Initialize Telnyx client
   */
  initialize() {
    try {
      if (!process.env.TELNYX_API_KEY) {
        console.warn('TELNYX_API_KEY not found. Telnyx service will use mock mode.');
        this.client = null;
        return;
      }

      // Initialize Telnyx client
      telnyx.setApiKey(process.env.TELNYX_API_KEY);
      this.client = telnyx;
      console.log('Telnyx service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Telnyx service:', error);
      this.client = null;
    }
  }

  /**
   * Generate a unique filename
   * @param {string} originalName - Original filename
   * @param {string} userId - User ID for organization
   * @returns {string} Unique filename
   */
  generateUniqueFilename(originalName, userId) {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    
    return `${userId}/${timestamp}-${randomString}-${baseName}${extension}`;
  }

  /**
   * Upload file to Telnyx storage
   * @param {Buffer|string} fileData - File data or file path
   * @param {string} filename - Filename
   * @param {string} contentType - MIME type
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Upload result
   */
  async uploadFile(fileData, filename, contentType, metadata = {}) {
    try {
      if (!this.client) {
        // Mock mode for development
        return this.mockUpload(filename, contentType, metadata);
      }

      // Prepare file data
      let buffer;
      if (typeof fileData === 'string') {
        // File path provided
        buffer = fs.readFileSync(fileData);
      } else {
        // Buffer provided
        buffer = fileData;
      }

      // Upload to Telnyx storage
      const uploadResult = await this.client.storage.upload({
        bucket: this.bucketName,
        key: filename,
        body: buffer,
        contentType: contentType,
        metadata: {
          ...metadata,
          uploadedAt: new Date().toISOString(),
          originalSize: buffer.length
        }
      });

      return {
        success: true,
        fileId: uploadResult.key,
        filename: filename,
        url: uploadResult.url || `https://storage.telnyx.com/${this.bucketName}/${filename}`,
        size: buffer.length,
        contentType: contentType,
        metadata: uploadResult.metadata
      };
    } catch (error) {
      console.error('File upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Download file from Telnyx storage
   * @param {string} fileId - File ID/key
   * @returns {Promise<Buffer>} File data
   */
  async downloadFile(fileId) {
    try {
      if (!this.client) {
        throw new Error('Telnyx service not available in mock mode');
      }

      const downloadResult = await this.client.storage.download({
        bucket: this.bucketName,
        key: fileId
      });

      return downloadResult.body;
    } catch (error) {
      console.error('File download error:', error);
      throw new Error(`Failed to download file: ${error.message}`);
    }
  }

  /**
   * Get file metadata
   * @param {string} fileId - File ID/key
   * @returns {Promise<Object>} File metadata
   */
  async getFileMetadata(fileId) {
    try {
      if (!this.client) {
        return this.mockGetMetadata(fileId);
      }

      const metadata = await this.client.storage.getMetadata({
        bucket: this.bucketName,
        key: fileId
      });

      return {
        success: true,
        fileId: fileId,
        size: metadata.size,
        contentType: metadata.contentType,
        lastModified: metadata.lastModified,
        metadata: metadata.metadata
      };
    } catch (error) {
      console.error('Get file metadata error:', error);
      throw new Error(`Failed to get file metadata: ${error.message}`);
    }
  }

  /**
   * Delete file from Telnyx storage
   * @param {string} fileId - File ID/key
   * @returns {Promise<boolean>} Success status
   */
  async deleteFile(fileId) {
    try {
      if (!this.client) {
        console.log(`Mock: Would delete file ${fileId}`);
        return true;
      }

      await this.client.storage.delete({
        bucket: this.bucketName,
        key: fileId
      });

      return true;
    } catch (error) {
      console.error('File deletion error:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * List files for a user
   * @param {string} userId - User ID
   * @param {number} limit - Maximum number of files to return
   * @returns {Promise<Array>} List of files
   */
  async listUserFiles(userId, limit = 50) {
    try {
      if (!this.client) {
        return this.mockListFiles(userId);
      }

      const listResult = await this.client.storage.list({
        bucket: this.bucketName,
        prefix: `${userId}/`,
        maxKeys: limit
      });

      return listResult.contents.map(file => ({
        fileId: file.key,
        filename: path.basename(file.key),
        size: file.size,
        lastModified: file.lastModified,
        url: `https://storage.telnyx.com/${this.bucketName}/${file.key}`
      }));
    } catch (error) {
      console.error('List files error:', error);
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  /**
   * Get storage usage statistics
   * @param {string} userId - User ID (optional)
   * @returns {Promise<Object>} Storage statistics
   */
  async getStorageStats(userId = null) {
    try {
      if (!this.client) {
        return this.mockGetStats(userId);
      }

      const prefix = userId ? `${userId}/` : '';
      const listResult = await this.client.storage.list({
        bucket: this.bucketName,
        prefix: prefix
      });

      const totalSize = listResult.contents.reduce((sum, file) => sum + file.size, 0);
      const fileCount = listResult.contents.length;

      return {
        success: true,
        fileCount: fileCount,
        totalSize: totalSize,
        totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
        userId: userId
      };
    } catch (error) {
      console.error('Get storage stats error:', error);
      throw new Error(`Failed to get storage statistics: ${error.message}`);
    }
  }

  /**
   * Validate file type and size
   * @param {string} filename - Filename
   * @param {number} fileSize - File size in bytes
   * @param {string} contentType - MIME type
   * @returns {Object} Validation result
   */
  validateFile(filename, fileSize, contentType) {
    const maxFileSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/webm', 'video/quicktime',
      'audio/mpeg', 'audio/wav', 'audio/ogg',
      'application/pdf', 'text/plain'
    ];

    const errors = [];

    if (fileSize > maxFileSize) {
      errors.push(`File size exceeds maximum limit of ${maxFileSize / (1024 * 1024)}MB`);
    }

    if (!allowedTypes.includes(contentType)) {
      errors.push(`File type ${contentType} is not allowed`);
    }

    const extension = path.extname(filename).toLowerCase();
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    if (dangerousExtensions.includes(extension)) {
      errors.push(`File extension ${extension} is not allowed`);
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  // Mock methods for development/testing
  mockUpload(filename, contentType, metadata) {
    return {
      success: true,
      fileId: `mock-${Date.now()}-${filename}`,
      filename: filename,
      url: `http://localhost:3000/mock-storage/${filename}`,
      size: 1024,
      contentType: contentType,
      metadata: { ...metadata, mock: true }
    };
  }

  mockGetMetadata(fileId) {
    return {
      success: true,
      fileId: fileId,
      size: 1024,
      contentType: 'application/octet-stream',
      lastModified: new Date().toISOString(),
      metadata: { mock: true }
    };
  }

  mockListFiles(userId) {
    return [
      {
        fileId: `${userId}/mock-file-1.jpg`,
        filename: 'mock-file-1.jpg',
        size: 1024,
        lastModified: new Date().toISOString(),
        url: `http://localhost:3000/mock-storage/${userId}/mock-file-1.jpg`
      }
    ];
  }

  mockGetStats(userId) {
    return {
      success: true,
      fileCount: userId ? 5 : 100,
      totalSize: userId ? 5120 : 102400,
      totalSizeMB: userId ? 0.01 : 0.1,
      userId: userId
    };
  }
}

// Export singleton instance
const telnyxService = new TelnyxService();
module.exports = telnyxService;
