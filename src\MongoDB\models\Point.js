const mongoose = require('mongoose');

const pointSchema = new mongoose.Schema({
  pointId: { 
    type: String, 
    required: true, 
    unique: true 
  },
  communityId: { 
    type: String, 
    required: true 
  },
  creatorId: { 
    type: String, 
    required: true 
  },
  position: {
    x: { type: Number, required: true },
    y: { type: Number, required: true },
    z: { type: Number, required: true }
  },
  metadata: {
    title: { type: String },
    description: { type: String },
    mediaUrls: [{ type: String }],
    color: { type: String },
    scale: { type: Number, default: 1.0 },
    // Add other custom metadata as needed
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  lastModified: { 
    type: Date, 
    default: Date.now 
  }
});

// Index on common queries
pointSchema.index({ pointId: 1 });
pointSchema.index({ communityId: 1 });
pointSchema.index({ creatorId: 1 });
pointSchema.index({ communityId: 1, creatorId: 1 });

module.exports = mongoose.model('Point', pointSchema);