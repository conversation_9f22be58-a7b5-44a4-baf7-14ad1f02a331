import TelnyxResource from '../TelnyxResource.js';
import * as utils from '../utils.js';
const telnyxMethod = TelnyxResource.method;
function transformResponseData(response, telnyx) {
    return utils.addResourceToResponseData(response, telnyx, 'simCardGroups', {
        del: telnyxMethod({
            method: 'DELETE',
            path: '/{simCardGroupId}',
            urlParams: ['simCardGroupId'],
            paramsValues: [response.data.id],
            paramsNames: ['id'],
        }),
        update: telnyxMethod({
            method: 'PATCH',
            path: '/{simCardGroupId}',
            urlParams: ['simCardGroupId'],
            paramsValues: [response.data.id],
            paramsNames: ['id'],
        }),
    });
}
export const SimCardGroups = TelnyxResource.extend({
    path: 'sim_card_groups',
    list: telnyxMethod({
        method: 'GET',
        methodType: 'list',
    }),
    create: telnyxMethod({
        method: 'POST',
        transformResponseData: transformResponseData,
    }),
    retrieve: telnyxMethod({
        method: 'GET',
        path: '/{id}',
        urlParams: ['id'],
        transformResponseData: transformResponseData,
    }),
});
