/**
 * Error handling middleware for the Homara backend
 * 
 * This file contains middleware functions for handling errors and 404 routes.
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 */

/**
 * Not found middleware
 * Handles requests to routes that don't exist
 */
const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

/**
 * Error handler middleware
 * Handles all errors in the application
 * 
 * In production, don't expose the stack trace to users
 */
const errorHandler = (err, req, res, next) => {
  // Log the error for server-side debugging
  console.error('Error:', err);
  
  // Set status code
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  res.status(statusCode);
  
  // Send error response
  res.json({
    message: err.message,
    // Only show stack trace in development
    stack: process.env.NODE_ENV === 'production' ? '🥞' : err.stack,
    // Include error code if available
    code: err.code || 'UNKNOWN_ERROR'
  });
};

module.exports = { notFound, errorHandler };
