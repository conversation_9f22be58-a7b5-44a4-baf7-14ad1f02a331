const Community = require('../models/Community');
const User = require('../models/User');
const { generateUniqueId } = require('../../utils/idGenerator');

// Create a new community
const createCommunity = async (communityData) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid: communityData.ownerUid });
    if (!user) throw new Error('User not found');
    
    // Generate a unique community ID
    const communityId = generateUniqueId('CMY');
    
    // Create the community
    const community = await Community.create({
      communityId,
      name: communityData.name,
      ownerId: user.pointId,
      settings: communityData.settings || {},
      members: [{
        pointId: user.pointId,
        firebaseUid: user.firebaseUid,
        role: 'founder',
        joinedAt: new Date()
      }]
    });
    
    // Update user with community ID (if they don't already have one)
    if (!user.communityId) {
      await User.updateOne(
        { _id: user._id },
        { communityId }
      );
    }
    
    return community;
  } catch (error) {
    throw error;
  }
};

// Get all communities for a user
const getUserCommunities = async (firebaseUid) => {
  try {
    const communities = await Community.find({
      'members.firebaseUid': firebaseUid
    });
    
    return communities;
  } catch (error) {
    throw error;
  }
};

// Get a specific community by ID
const getCommunityById = async (communityId, firebaseUid) => {
  try {
    const community = await Community.findOne({ communityId });
    
    // If community not found
    if (!community) return null;
    
    // If community is private, check if user is a member
    if (community.settings.visibility === 'private') {
      const isMember = community.members.some(
        member => member.firebaseUid === firebaseUid
      );
      
      if (!isMember) {
        throw new Error('You do not have permission to view this community');
      }
    }
    
    return community;
  } catch (error) {
    throw error;
  }
};

// Get a public community by ID (no auth required)
const getPublicCommunityById = async (communityId) => {
  try {
    const community = await Community.findOne({
      communityId,
      'settings.visibility': 'public'
    });
    
    return community;
  } catch (error) {
    throw error;
  }
};

// Add a member to a community
const addCommunityMember = async (communityId, userPointId, firebaseUid, role = 'member') => {
  try {
    const community = await Community.findOne({ communityId });
    if (!community) throw new Error('Community not found');
    
    // Check if user is already a member
    const existingMember = community.members.find(
      member => member.firebaseUid === firebaseUid
    );
    
    if (existingMember) {
      throw new Error('User is already a member of this community');
    }
    
    // Add member to community
    community.members.push({
      pointId: userPointId,
      firebaseUid,
      role,
      joinedAt: new Date()
    });
    
    await community.save();
    return community;
  } catch (error) {
    throw error;
  }
};

// Check if a user is a founder/admin of a community
const isUserAdminOfCommunity = async (firebaseUid, communityId) => {
  try {
    const community = await Community.findOne({ communityId });
    if (!community) return false;
    
    const member = community.members.find(m => m.firebaseUid === firebaseUid);
    if (!member) return false;
    
    return ['founder', 'admin'].includes(member.role);
  } catch (error) {
    return false;
  }
};

module.exports = {
  createCommunity,
  getUserCommunities,
  getCommunityById,
  getPublicCommunityById,
  addCommunityMember,
  isUserAdminOfCommunity
};